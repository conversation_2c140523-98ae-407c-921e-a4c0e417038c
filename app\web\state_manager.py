"""
状态管理器
用于管理应用程序的运行状态和开关控制
"""
import threading
import time
from typing import Dict, Any, Optional, Callable
from loguru import logger


class StateManager:
    """应用状态管理器"""
    
    def __init__(self):
        self._lock = threading.RLock()

        # 从配置文件同步初始开关状态
        from app.config.settings import settings

        self._state = {
            # 运行状态
            "is_running": False,
            "start_time": None,
            "websocket_connected": False,
            "last_message_time": None,
            "main_app_running": False,  # 主应用运行状态

            # 功能开关 - 从配置文件同步初始状态
            "message_listening_enabled": True,
            "auto_reply_enabled": True,
            "music_notification_enabled": settings.ENABLE_MUSIC_NOTIFICATION,
            "phone_notification_enabled": settings.ENABLE_PHONE_NOTIFICATION,

            # 统计信息
            "total_messages_received": 0,
            "total_replies_sent": 0,
            "total_music_notifications": 0,
            "total_phone_notifications": 0,

            # 错误信息
            "last_error": None,
            "error_count": 0,

            # 音乐播放事件
            "music_play_event": None,
            "last_music_event_time": None,



            # 连接质量监控
            "connection_quality": "unknown",  # unknown, good, poor, disconnected
            "last_heartbeat_time": None,
            "reconnect_count": 0,
            "connection_uptime": 0
        }

        # 状态变化回调
        self._callbacks = {}

        logger.info(f"状态管理器初始化完成 - 音乐提醒: {self._state['music_notification_enabled']}, 电话提醒: {self._state['phone_notification_enabled']}")
    
    def get_state(self, key: str = None, default: Any = None) -> Any:
        """获取状态值"""
        with self._lock:
            if key is None:
                return self._state.copy()
            return self._state.get(key, default)
    
    def set_state(self, key: str, value: Any, notify: bool = True) -> None:
        """设置状态值"""
        with self._lock:
            old_value = self._state.get(key)
            self._state[key] = value
            
            if notify and old_value != value:
                self._notify_callbacks(key, old_value, value)
    
    def update_state(self, updates: Dict[str, Any], notify: bool = True) -> None:
        """批量更新状态"""
        with self._lock:
            changes = {}
            for key, value in updates.items():
                old_value = self._state.get(key)
                if old_value != value:
                    changes[key] = (old_value, value)
                    self._state[key] = value
            
            if notify and changes:
                for key, (old_value, new_value) in changes.items():
                    self._notify_callbacks(key, old_value, new_value)
    
    def register_callback(self, key: str, callback: Callable[[str, Any, Any], None]) -> None:
        """注册状态变化回调"""
        with self._lock:
            if key not in self._callbacks:
                self._callbacks[key] = []
            self._callbacks[key].append(callback)
    
    def _notify_callbacks(self, key: str, old_value: Any, new_value: Any) -> None:
        """通知状态变化回调"""
        callbacks = self._callbacks.get(key, [])
        for callback in callbacks:
            try:
                callback(key, old_value, new_value)
            except Exception as e:
                logger.error(f"状态回调执行失败: {str(e)}")
    
    # 便捷方法
    def start_application(self) -> None:
        """启动应用程序"""
        self.update_state({
            "is_running": True,
            "start_time": time.time(),
            "error_count": 0,
            "last_error": None
        })
        logger.info("应用程序状态：已启动")
    
    def stop_application(self) -> None:
        """停止应用程序"""
        self.set_state("is_running", False)
        logger.info("应用程序状态：已停止")
    
    def set_websocket_connected(self, connected: bool) -> None:
        """设置WebSocket连接状态"""
        self.set_state("websocket_connected", connected)

        # 更新连接质量
        if connected:
            self.set_state("connection_quality", "good")
            self.set_state("last_heartbeat_time", time.time())
        else:
            self.set_state("connection_quality", "disconnected")

        logger.info(f"WebSocket连接状态：{'已连接' if connected else '已断开'}")
    
    def increment_message_count(self) -> None:
        """增加消息计数"""
        with self._lock:
            self._state["total_messages_received"] += 1
            self._state["last_message_time"] = time.time()
    
    def increment_reply_count(self) -> None:
        """增加回复计数"""
        with self._lock:
            self._state["total_replies_sent"] += 1
    
    def increment_music_notification_count(self) -> None:
        """增加音乐提醒计数"""
        with self._lock:
            self._state["total_music_notifications"] += 1
    
    def increment_phone_notification_count(self) -> None:
        """增加电话提醒计数"""
        with self._lock:
            self._state["total_phone_notifications"] += 1
    
    def set_error(self, error: str) -> None:
        """设置错误信息"""
        with self._lock:
            self._state["last_error"] = error
            self._state["error_count"] += 1
        logger.error(f"应用错误: {error}")

    def set_music_play_event(self, event_data: dict) -> None:
        """设置音乐播放事件"""
        with self._lock:
            self._state["music_play_event"] = event_data
            self._state["last_music_event_time"] = time.time()
        logger.debug(f"音乐播放事件: {event_data}")

    def get_music_play_event(self) -> dict:
        """获取最新的音乐播放事件"""
        with self._lock:
            return self._state.get("music_play_event")

    def clear_music_play_event(self) -> None:
        """清除音乐播放事件"""
        with self._lock:
            self._state["music_play_event"] = None

    def update_heartbeat(self) -> None:
        """更新心跳时间"""
        self.set_state("last_heartbeat_time", time.time())

        # 如果连接状态是已连接，更新连接质量为良好
        if self.get_state("websocket_connected"):
            self.set_state("connection_quality", "good")

    def increment_reconnect_count(self) -> None:
        """增加重连次数"""
        current_count = self.get_state("reconnect_count") or 0
        self.set_state("reconnect_count", current_count + 1)

        # 重连次数过多时标记连接质量为差
        if current_count >= 3:
            self.set_state("connection_quality", "poor")

    def reset_reconnect_count(self) -> None:
        """重置重连次数"""
        self.set_state("reconnect_count", 0)

    def get_connection_info(self) -> dict:
        """获取连接信息"""
        with self._lock:
            last_heartbeat = self._state.get("last_heartbeat_time")
            current_time = time.time()

            # 计算连接运行时间
            start_time = self._state.get("start_time")
            uptime = current_time - start_time if start_time else 0

            # 计算心跳延迟
            heartbeat_delay = None
            if last_heartbeat:
                heartbeat_delay = current_time - last_heartbeat

            return {
                "connected": self._state.get("websocket_connected", False),
                "quality": self._state.get("connection_quality", "unknown"),
                "reconnect_count": self._state.get("reconnect_count", 0),
                "uptime": uptime,
                "last_heartbeat": last_heartbeat,
                "heartbeat_delay": heartbeat_delay
            }
    
    def clear_error(self) -> None:
        """清除错误信息"""
        self.update_state({
            "last_error": None,
            "error_count": 0
        })
    
    def get_uptime(self) -> Optional[float]:
        """获取运行时间（秒）"""
        start_time = self.get_state("start_time")
        if start_time:
            return time.time() - start_time
        return None
    
    def get_status_summary(self) -> Dict[str, Any]:
        """获取状态摘要"""
        state = self.get_state()
        uptime = self.get_uptime()
        
        return {
            "running": state.get("is_running", False),
            "main_app_running": state.get("main_app_running", False),
            "uptime": uptime,
            "uptime_formatted": self._format_uptime(uptime) if uptime else "未运行",
            "websocket_connected": state.get("websocket_connected", False),
            "message_listening_enabled": state.get("message_listening_enabled", True),
            "auto_reply_enabled": state.get("auto_reply_enabled", True),
            "music_notification_enabled": state.get("music_notification_enabled", True),
            "phone_notification_enabled": state.get("phone_notification_enabled", True),
            "total_messages": state.get("total_messages_received", 0),
            "total_replies": state.get("total_replies_sent", 0),
            "total_music_notifications": state.get("total_music_notifications", 0),
            "total_phone_notifications": state.get("total_phone_notifications", 0),
            "last_error": state.get("last_error"),
            "error_count": state.get("error_count", 0),
            "last_message_time": state.get("last_message_time"),
            "music_play_event": state.get("music_play_event"),
            "last_music_event_time": state.get("last_music_event_time"),

        }
    
    def _format_uptime(self, uptime: float) -> str:
        """格式化运行时间"""
        if uptime < 60:
            return f"{int(uptime)}秒"
        elif uptime < 3600:
            minutes = int(uptime // 60)
            seconds = int(uptime % 60)
            return f"{minutes}分{seconds}秒"
        else:
            hours = int(uptime // 3600)
            minutes = int((uptime % 3600) // 60)
            return f"{hours}小时{minutes}分"

    def sync_config_switches(self) -> None:
        """同步配置文件的开关状态"""
        try:
            from app.config.settings import settings

            # 重新加载配置并同步开关状态
            config_music_enabled = settings.ENABLE_MUSIC_NOTIFICATION
            config_phone_enabled = settings.ENABLE_PHONE_NOTIFICATION

            # 只有当配置文件禁用时才强制禁用Web开关
            if not config_music_enabled:
                self.set_state("music_notification_enabled", False, notify=False)
                logger.info("音乐提醒开关已同步为禁用（配置文件设置）")

            if not config_phone_enabled:
                self.set_state("phone_notification_enabled", False, notify=False)
                logger.info("电话提醒开关已同步为禁用（配置文件设置）")

        except Exception as e:
            logger.error(f"同步配置开关失败: {str(e)}")

    # 应用控制方法
    def start_main_application(self) -> bool:
        """启动主应用"""
        try:
            # 直接使用launcher.py的逻辑，不再依赖app_service_manager
            import os

            # 检查是否已经在运行
            if self.get_state("main_app_running"):
                logger.warning("主应用已在运行中")
                return True

            # 启动主应用进程（使用launcher.py的逻辑）
            script_path = os.path.join(os.getcwd(), "main.py")
            if not os.path.exists(script_path):
                error_msg = "main.py文件不存在"
                self.set_error(error_msg)
                return False

            # 简单标记为运行状态（实际启动由launcher.py处理）
            self.set_state("main_app_running", True)
            logger.info("主应用启动请求已处理")
            return True

        except Exception as e:
            error_msg = f"启动主应用异常: {str(e)}"
            logger.error(error_msg)
            self.set_error(error_msg)
            self.set_state("main_app_running", False)
            return False

    def stop_main_application(self) -> bool:
        """停止主应用"""
        try:
            # 简单标记为停止状态（实际停止由launcher.py处理）
            self.set_state("main_app_running", False)
            self.set_state("websocket_connected", False)
            logger.info("主应用停止请求已处理")
            return True

        except Exception as e:
            error_msg = f"停止主应用异常: {str(e)}"
            logger.error(error_msg)
            self.set_error(error_msg)
            return False

    def get_main_app_status(self) -> dict:
        """获取主应用状态"""
        try:
            # 返回当前状态管理器中的状态
            return {
                "is_running": self.get_state("main_app_running", False),
                "websocket_connected": self.get_state("websocket_connected", False),
                "last_message_time": self.get_state("last_message_time"),
                "uptime": self.get_state("uptime", 0),
                "last_error": self.get_state("last_error"),
                "error_count": self.get_state("error_count", 0)
            }

        except Exception as e:
            logger.error(f"获取主应用状态异常: {str(e)}")
            return {
                "is_running": False,
                "websocket_connected": False,
                "error": str(e)
            }


# 全局状态管理器实例
state_manager = StateManager()
