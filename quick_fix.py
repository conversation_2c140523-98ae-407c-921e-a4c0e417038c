#!/usr/bin/env python3
"""
LarkAgentX 一键修复脚本
自动修复常见配置问题
"""
import shutil
import sys
from pathlib import Path

def print_header():
    """打印标题"""
    print("=" * 60)
    print("🔧 LarkAgentX 一键修复脚本")
    print("=" * 60)

def check_music_file():
    """检查和修复音乐文件问题"""
    print("\n📁 检查音乐文件配置...")
    
    # 创建音频目录
    audio_dir = Path("static/audio")
    audio_dir.mkdir(parents=True, exist_ok=True)
    print(f"✅ 音频目录已创建: {audio_dir}")
    
    # 目标文件路径
    target_file = audio_dir / "notification.mp3"
    
    if target_file.exists():
        print(f"✅ 默认音乐文件已存在: {target_file}")
        return True
    
    # 检查常见音乐文件位置
    possible_files = [
        "D:/Personal/Downloads/zfb100w.mp3",
        "D:/个人开发/Feishu_Windows_speak_text-main/dist/music.mp3",
        "music.mp3",
        "notification.mp3",
        "alert.mp3"
    ]
    
    print("🔍 搜索可用的音乐文件...")
    for file_path in possible_files:
        path = Path(file_path)
        if path.exists():
            try:
                shutil.copy2(path, target_file)
                print(f"✅ 音乐文件已复制: {path} -> {target_file}")
                return True
            except Exception as e:
                print(f"❌ 复制失败: {str(e)}")
    
    print("⚠️  未找到音乐文件，请手动复制音乐文件到:")
    print(f"   {target_file.absolute()}")
    print("   支持格式: MP3, WAV, OGG")
    return False

def fix_env_config():
    """修复.env配置文件"""
    print("\n⚙️  检查配置文件...")
    
    env_file = Path(".env")
    
    # 默认配置
    default_config = {
        "NOTIFICATION_MUSIC_FILE": "static/audio/notification.mp3",
        "ENABLE_MUSIC_NOTIFICATION": "true",
        "ENABLE_PHONE_NOTIFICATION": "true",
        "TRIGGER_PATTERN": "已接通人工.*?@.*?为你服务.*?请问.*?帮你",
        "REPLY_MESSAGE": "您好！有什么可以帮您？",
        "NOTIFICATION_TITLE": "飞书消息提醒",
        "NOTIFICATION_MESSAGE": "您有新的飞书消息！\\n点击确定停止音乐提醒",
        "PHONE_NOTIFICATION_INTERVAL": "15"
    }
    
    # 读取现有配置
    existing_config = {}
    if env_file.exists():
        try:
            with open(env_file, "r", encoding="utf-8") as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith("#") and "=" in line:
                        key, value = line.split("=", 1)
                        existing_config[key.strip()] = value.strip().strip('"\'')
        except Exception as e:
            print(f"❌ 读取配置文件失败: {str(e)}")
    
    # 合并配置
    updated = False
    for key, default_value in default_config.items():
        if key not in existing_config:
            existing_config[key] = default_value
            updated = True
            print(f"➕ 添加配置: {key}={default_value}")
    
    # 保存配置
    if updated or not env_file.exists():
        try:
            with open(env_file, "w", encoding="utf-8") as f:
                f.write("# LarkAgentX 配置文件\n")
                f.write("# 自动生成于一键修复脚本\n\n")
                
                for key, value in existing_config.items():
                    f.write(f'{key}="{value}"\n')
            
            print(f"✅ 配置文件已更新: {env_file.absolute()}")
        except Exception as e:
            print(f"❌ 保存配置文件失败: {str(e)}")
            return False
    else:
        print("✅ 配置文件无需更新")
    
    return True

def check_dependencies():
    """检查依赖包"""
    print("\n📦 检查依赖包...")
    
    required_packages = [
        "flask",
        "loguru",
        "python-dotenv",
        "requests",
        "websockets"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} (缺失)")
    
    if missing_packages:
        print(f"\n⚠️  发现缺失的依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def create_example_files():
    """创建示例文件"""
    print("\n📄 创建示例文件...")
    
    # 创建示例.env文件
    env_example = Path(".env.example")
    if not env_example.exists():
        example_content = '''# LarkAgentX 配置示例文件
# 复制此文件为 .env 并填写实际值

# 飞书认证配置 (必需)
LARK_COOKIE="your_lark_cookie_here"

# 消息匹配和回复配置
TRIGGER_PATTERN="已接通人工.*?@.*?为你服务.*?请问.*?帮你"
REPLY_MESSAGE="您好！有什么可以帮您？"

# 音乐提醒配置
ENABLE_MUSIC_NOTIFICATION=true
NOTIFICATION_MUSIC_FILE="static/audio/notification.mp3"
NOTIFICATION_TITLE="飞书消息提醒"
NOTIFICATION_MESSAGE="您有新的飞书消息！\\n点击确定停止音乐提醒"

# 电话提醒配置
ENABLE_PHONE_NOTIFICATION=true
LARK_APP_ID="your_app_id"
LARK_APP_SECRET="your_app_secret"
PHONE_NOTIFICATION_USER_IDS="user1,user2"
PHONE_NOTIFICATION_MESSAGE_IDS="msg1,msg2,msg3,msg4"
PHONE_NOTIFICATION_INTERVAL=15
'''
        try:
            with open(env_example, "w", encoding="utf-8") as f:
                f.write(example_content)
            print(f"✅ 创建示例配置文件: {env_example}")
        except Exception as e:
            print(f"❌ 创建示例文件失败: {str(e)}")

def print_summary():
    """打印修复总结"""
    print("\n" + "=" * 60)
    print("🎉 修复完成！")
    print("=" * 60)
    print("\n📋 下一步操作:")
    print("1. 确保音乐文件存在于 static/audio/notification.mp3")
    print("2. 在 .env 文件中配置飞书Cookie")
    print("3. 运行程序: python main.py")
    print("4. 访问Web界面: http://127.0.0.1:8080")
    print("\n💡 提示:")
    print("- 如果仍有问题，请查看 docs/QUICK_FIX.md")
    print("- 使用Web界面监控状态: http://127.0.0.1:8080")

def main():
    """主函数"""
    print_header()
    
    success_count = 0
    total_checks = 4
    
    # 检查音乐文件
    if check_music_file():
        success_count += 1
    
    # 修复配置文件
    if fix_env_config():
        success_count += 1
    
    # 检查依赖包
    if check_dependencies():
        success_count += 1
    
    # 创建示例文件
    create_example_files()
    success_count += 1
    
    print(f"\n📊 修复结果: {success_count}/{total_checks} 项检查通过")
    
    if success_count == total_checks:
        print("✅ 所有检查都通过了！")
    else:
        print("⚠️  部分检查未通过，请查看上面的详细信息")
    
    print_summary()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 修复过程中出现错误: {str(e)}")
        sys.exit(1)
