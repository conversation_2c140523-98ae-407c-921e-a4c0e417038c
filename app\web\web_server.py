"""
Web管理服务器
提供配置管理和状态控制的Web界面
"""
import os
import threading
import time
import logging
from flask import Flask, render_template, request, jsonify, send_file
from loguru import logger
from .config_manager import config_manager
from .state_manager import state_manager


class WebServer:
    """Web管理服务器"""
    
    def __init__(self, host: str = "127.0.0.1", port: int = 8080):
        self.host = host
        self.port = port
        self.app = Flask(__name__, 
                        template_folder=os.path.join(os.path.dirname(__file__), 'templates'),
                        static_folder=os.path.join(os.path.dirname(__file__), 'static'))
        self.app.secret_key = 'larkagentx_web_secret_key'
        self.server_thread = None
        self.is_running = False
        
        self._setup_routes()
    
    def _setup_routes(self):
        """设置路由"""
        
        @self.app.route('/')
        def index():
            """首页 - 状态概览"""
            status = state_manager.get_status_summary()
            return render_template('index.html', status=status)
        
        @self.app.route('/config')
        def config():
            """配置页面"""
            config_data = config_manager.get_all_config()
            config_schema = config_manager.get_config_schema()
            return render_template('config.html', 
                                 config=config_data, 
                                 schema=config_schema)
        
        @self.app.route('/logs')
        def logs():
            """日志页面"""
            return render_template('logs.html')

        @self.app.route('/favicon.ico')
        def favicon():
            """Favicon图标"""
            try:
                # 项目根目录的feishu.ico文件
                favicon_path = os.path.join(os.getcwd(), 'feishu.ico')
                if os.path.exists(favicon_path):
                    return send_file(favicon_path, mimetype='image/x-icon')
                else:
                    # 如果文件不存在，返回404
                    return '', 404
            except Exception as e:
                logger.error(f"Favicon服务失败: {str(e)}")
                return '', 404

        # API路由
        @self.app.route('/api/status')
        def api_status():
            """获取状态信息API"""
            # 先检查主应用状态，确保状态同步
            state_manager.get_main_app_status()
            return jsonify(state_manager.get_status_summary())
        
        @self.app.route('/api/toggle/<switch_name>', methods=['POST'])
        def api_toggle_switch(switch_name):
            """切换开关状态API"""
            try:
                data = request.get_json() or {}
                enabled = data.get('enabled', True)
                
                switch_mapping = {
                    'message_listening': 'message_listening_enabled',
                    'auto_reply': 'auto_reply_enabled', 
                    'music_notification': 'music_notification_enabled',
                    'phone_notification': 'phone_notification_enabled'
                }
                
                if switch_name not in switch_mapping:
                    return jsonify({'success': False, 'error': '无效的开关名称'}), 400
                
                state_key = switch_mapping[switch_name]
                state_manager.set_state(state_key, enabled)
                
                logger.info(f"开关状态变更: {switch_name} = {enabled}")
                return jsonify({'success': True, 'enabled': enabled})
                
            except Exception as e:
                logger.error(f"切换开关失败: {str(e)}")
                return jsonify({'success': False, 'error': str(e)}), 500
        
        @self.app.route('/api/config', methods=['GET', 'POST'])
        def api_config():
            """配置管理API"""
            if request.method == 'GET':
                # 验证配置状态
                config_status = self._validate_config()
                return jsonify({
                    'config': config_manager.get_all_config(),
                    'schema': config_manager.get_config_schema(),
                    'validation': config_status
                })
            
            elif request.method == 'POST':
                try:
                    data = request.get_json()
                    if not data:
                        return jsonify({'success': False, 'error': '无效的数据'}), 400
                    
                    # 更新配置（只更新实际提交的配置项）
                    logger.info(f"收到配置更新请求: {data}")
                    for key, value in data.items():
                        if value is not None and value != "":
                            logger.info(f"更新配置项: {key} = {value}")
                            config_manager.set_config(key, str(value))

                    # 保存到文件
                    if config_manager.save_config():
                        logger.info("配置文件保存成功，开始重新加载...")
                        # 重新加载应用配置
                        try:
                            from app.config.settings import settings
                            settings.reload()
                            logger.info("配置更新并重新加载成功")

                            # 验证配置是否真的更新了
                            current_mode = settings.MESSAGE_MATCH_MODE
                            logger.info(f"当前消息匹配模式: {current_mode}")

                            return jsonify({'success': True, 'message': '配置保存成功，已重新加载'})
                        except Exception as e:
                            logger.error(f"配置重新加载失败: {str(e)}")
                            return jsonify({'success': True, 'message': '配置保存成功，但重新加载失败，请重启应用'})
                    else:
                        return jsonify({'success': False, 'error': '配置保存失败'}), 500
                        
                except Exception as e:
                    logger.error(f"更新配置失败: {str(e)}")
                    return jsonify({'success': False, 'error': str(e)}), 500
        
        @self.app.route('/api/restart', methods=['POST'])
        def api_restart():
            """重启应用API"""
            try:
                # 这里可以添加重启逻辑
                logger.info("收到重启请求")
                return jsonify({'success': True, 'message': '重启请求已接收'})
            except Exception as e:
                logger.error(f"重启失败: {str(e)}")
                return jsonify({'success': False, 'error': str(e)}), 500

        @self.app.route('/api/app/start', methods=['POST'])
        def api_start_app():
            """启动主应用API"""
            try:
                success = state_manager.start_main_application()
                if success:
                    return jsonify({"success": True, "message": "主应用启动成功"})
                else:
                    return jsonify({"success": False, "error": "主应用启动失败"})
            except Exception as e:
                logger.error(f"启动主应用API异常: {str(e)}")
                return jsonify({"success": False, "error": str(e)})

        @self.app.route('/api/app/stop', methods=['POST'])
        def api_stop_app():
            """停止主应用API"""
            try:
                success = state_manager.stop_main_application()
                if success:
                    return jsonify({"success": True, "message": "主应用停止成功"})
                else:
                    return jsonify({"success": False, "error": "主应用停止失败"})
            except Exception as e:
                logger.error(f"停止主应用API异常: {str(e)}")
                return jsonify({"success": False, "error": str(e)})

        @self.app.route('/api/app/status')
        def api_app_status():
            """获取主应用详细状态API"""
            try:
                status = state_manager.get_main_app_status()
                return jsonify(status)
            except Exception as e:
                logger.error(f"获取主应用状态API异常: {str(e)}")
                return jsonify({"error": str(e)})


        @self.app.route('/api/logs')
        def api_logs():
            """获取日志API"""
            try:
                lines = request.args.get('lines', 100, type=int)
                level_filter = request.args.get('level', '')
                search_query = request.args.get('search', '')
                message_only = request.args.get('message_only', 'false').lower() == 'true'

                logs = self._read_log_files(lines, level_filter, search_query, message_only)

                return jsonify({'logs': logs})
            except Exception as e:
                logger.error(f"获取日志失败: {str(e)}")
                return jsonify({'logs': []}), 500

        @self.app.route('/api/audio/<path:filename>')
        def api_audio(filename):
            """音频文件服务API"""
            try:
                # 安全检查：防止路径遍历攻击
                if '..' in filename or filename.startswith('/'):
                    return jsonify({'error': '无效的文件路径'}), 400

                # 构建音频文件的完整路径
                audio_file_path = os.path.join(os.getcwd(), filename)

                # 检查文件是否存在
                if not os.path.exists(audio_file_path):
                    logger.warning(f"音频文件不存在: {audio_file_path}")
                    return jsonify({'error': '音频文件不存在'}), 404

                # 检查文件扩展名
                allowed_extensions = {'.mp3', '.wav', '.ogg', '.m4a', '.aac'}
                file_ext = os.path.splitext(filename)[1].lower()
                if file_ext not in allowed_extensions:
                    return jsonify({'error': '不支持的音频格式'}), 400

                logger.info(f"提供音频文件: {audio_file_path}")
                return send_file(audio_file_path, as_attachment=False)

            except Exception as e:
                logger.error(f"音频文件服务失败: {str(e)}")
                return jsonify({'error': f'音频文件服务失败: {str(e)}'}), 500

        @self.app.route('/api/music-event')
        def api_music_event():
            """获取音乐播放事件API（调试用）"""
            try:
                event = state_manager.get_music_play_event()
                return jsonify({
                    'success': True,
                    'event': event,
                    'timestamp': time.time()
                })
            except Exception as e:
                logger.error(f"获取音乐事件失败: {str(e)}")
                return jsonify({'success': False, 'error': str(e)}), 500

        @self.app.route('/api/upload-audio', methods=['POST'])
        def api_upload_audio():
            """上传音频文件API"""
            try:
                if 'audio_file' not in request.files:
                    return jsonify({'success': False, 'error': '没有选择文件'}), 400

                file = request.files['audio_file']
                if file.filename == '':
                    return jsonify({'success': False, 'error': '没有选择文件'}), 400

                # 检查文件扩展名
                allowed_extensions = {'.mp3', '.wav', '.ogg', '.m4a', '.aac'}
                file_ext = os.path.splitext(file.filename)[1].lower()
                if file_ext not in allowed_extensions:
                    return jsonify({'success': False, 'error': f'不支持的音频格式: {file_ext}'}), 400

                # 创建音频目录
                audio_dir = os.path.join(os.getcwd(), 'static', 'audio')
                os.makedirs(audio_dir, exist_ok=True)

                # 保存文件
                filename = file.filename
                file_path = os.path.join(audio_dir, filename)
                file.save(file_path)

                # 更新配置文件中的音频路径
                relative_path = f"static/audio/{filename}"
                config_manager.set_config('NOTIFICATION_MUSIC_FILE', relative_path)
                config_manager.save_config()

                # 重新加载配置
                try:
                    from app.config.settings import settings
                    settings.reload()
                except Exception as e:
                    logger.warning(f"配置重新加载失败: {str(e)}")

                logger.info(f"音频文件上传成功: {file_path}")
                return jsonify({
                    'success': True,
                    'message': '音频文件上传成功',
                    'filename': filename,
                    'path': relative_path
                })

            except Exception as e:
                logger.error(f"音频文件上传失败: {str(e)}")
                return jsonify({'success': False, 'error': f'上传失败: {str(e)}'}), 500

        @self.app.route('/api/list-audio')
        def api_list_audio():
            """列出可用的音频文件API"""
            try:
                audio_dir = os.path.join(os.getcwd(), 'static', 'audio')
                audio_files = []

                if os.path.exists(audio_dir):
                    allowed_extensions = {'.mp3', '.wav', '.ogg', '.m4a', '.aac'}
                    for filename in os.listdir(audio_dir):
                        if os.path.splitext(filename)[1].lower() in allowed_extensions:
                            file_path = os.path.join(audio_dir, filename)
                            file_size = os.path.getsize(file_path)
                            audio_files.append({
                                'filename': filename,
                                'path': f"static/audio/{filename}",
                                'size': file_size,
                                'size_mb': round(file_size / (1024 * 1024), 2)
                            })

                return jsonify({
                    'success': True,
                    'files': audio_files
                })

            except Exception as e:
                logger.error(f"列出音频文件失败: {str(e)}")
                return jsonify({'success': False, 'error': str(e)}), 500

        @self.app.route('/api/select-audio', methods=['POST'])
        def api_select_audio():
            """选择音频文件API"""
            try:
                data = request.get_json()
                if not data or 'filename' not in data:
                    return jsonify({'success': False, 'error': '缺少文件名参数'}), 400

                filename = data['filename']
                relative_path = f"static/audio/{filename}"

                # 检查文件是否存在
                file_path = os.path.join(os.getcwd(), relative_path)
                if not os.path.exists(file_path):
                    return jsonify({'success': False, 'error': '文件不存在'}), 404

                # 更新配置
                config_manager.set_config('NOTIFICATION_MUSIC_FILE', relative_path)
                config_manager.save_config()

                # 重新加载配置
                try:
                    from app.config.settings import settings
                    settings.reload()
                except Exception as e:
                    logger.warning(f"配置重新加载失败: {str(e)}")

                logger.info(f"音频文件已选择: {relative_path}")
                return jsonify({
                    'success': True,
                    'message': '音频文件选择成功',
                    'path': relative_path
                })

            except Exception as e:
                logger.error(f"选择音频文件失败: {str(e)}")
                return jsonify({'success': False, 'error': str(e)}), 500

        @self.app.route('/api/stop-music-notification', methods=['POST'])
        def api_stop_music_notification():
            """停止音乐提醒API"""
            try:
                from app.utils.music_player import music_player

                # 停止音乐播放
                music_player.stop_playing()

                logger.info("用户通过Web界面停止了音乐提醒")
                return jsonify({
                    'success': True,
                    'message': '音乐提醒已停止'
                })

            except Exception as e:
                logger.error(f"停止音乐提醒失败: {str(e)}")
                return jsonify({'success': False, 'error': str(e)}), 500

        @self.app.route('/api/test-music-notification', methods=['POST'])
        def api_test_music_notification():
            """测试音乐提醒API"""
            try:
                from app.utils.music_player import music_player
                from app.config.settings import settings
                import os

                # 获取当前音乐文件路径
                music_file = settings.get_current_music_file()

                # 检查音乐提醒开关
                if not state_manager.get_state("music_notification_enabled"):
                    return jsonify({
                        'success': False,
                        'error': '音乐提醒功能已禁用，请先启用音乐提醒开关'
                    })

                # 检查音乐文件是否存在
                if not os.path.exists(music_file):
                    return jsonify({
                        'success': False,
                        'error': f'音乐文件不存在: {music_file}',
                        'suggestion': '请在配置页面设置正确的音乐文件路径，或将音乐文件放到 static/audio/ 目录'
                    })

                # 检查文件格式
                allowed_extensions = {'.mp3', '.wav', '.ogg', '.m4a', '.aac'}
                file_ext = os.path.splitext(music_file)[1].lower()
                if file_ext not in allowed_extensions:
                    return jsonify({
                        'success': False,
                        'error': f'不支持的音频格式: {file_ext}',
                        'suggestion': f'请使用支持的格式: {", ".join(allowed_extensions)}'
                    })

                # 启动测试播放
                music_player.start_playing(music_file)

                logger.info(f"用户通过Web界面测试音乐播放: {music_file}")
                return jsonify({
                    'success': True,
                    'message': '音乐测试播放已启动',
                    'music_file': music_file,
                    'info': '音乐将在浏览器中播放，如果没有听到声音，请检查浏览器音频权限'
                })

            except Exception as e:
                logger.error(f"测试音乐播放失败: {str(e)}")
                return jsonify({
                    'success': False,
                    'error': f'测试播放失败: {str(e)}',
                    'suggestion': '请检查音乐文件是否损坏或路径是否正确'
                }), 500

        @self.app.route('/api/clear-music-event', methods=['POST'])
        def api_clear_music_event():
            """清除音乐播放事件API"""
            try:
                state_manager.clear_music_play_event()
                logger.debug("音乐播放事件已清除")
                return jsonify({
                    'success': True,
                    'message': '音乐事件已清除'
                })
            except Exception as e:
                logger.error(f"清除音乐事件失败: {str(e)}")
                return jsonify({'success': False, 'error': str(e)}), 500



        @self.app.route('/api/connection-info')
        def api_connection_info():
            """获取连接信息API"""
            try:
                connection_info = state_manager.get_connection_info()
                return jsonify({
                    'success': True,
                    'connection_info': connection_info
                })
            except Exception as e:
                logger.error(f"获取连接信息失败: {str(e)}")
                return jsonify({'success': False, 'error': str(e)}), 500
        
        @self.app.errorhandler(404)
        def not_found(error):
            return render_template('404.html'), 404
        
        @self.app.errorhandler(500)
        def internal_error(error):
            return render_template('500.html'), 500

    def _read_log_files(self, lines=100, level_filter='', search_query='', message_only=False):
        """读取日志文件"""
        import re
        from pathlib import Path

        logs = []
        log_files = [
            'logs/launcher.log',
            'logs/larkagentx.log'  # 备用日志文件
        ]

        for log_file in log_files:
            log_path = Path(log_file)
            if not log_path.exists():
                continue

            try:
                with open(log_path, 'r', encoding='utf-8') as f:
                    file_logs = self._parse_log_file(f, lines, level_filter, search_query, message_only)
                    logs.extend(file_logs)
            except Exception as e:
                logger.error(f"读取日志文件失败 {log_file}: {str(e)}")
                continue

        # 按时间排序，最新的在前
        logs.sort(key=lambda x: x.get('timestamp', ''), reverse=True)

        # 限制返回的日志数量
        return logs[:lines]

    def _parse_log_file(self, file_handle, lines, level_filter, search_query, message_only=False):
        """解析日志文件内容"""
        import re
        from datetime import datetime

        logs = []
        file_lines = file_handle.readlines()

        # 从文件末尾开始读取，获取最新的日志
        file_lines = file_lines[-lines*3:] if len(file_lines) > lines*3 else file_lines

        for line in file_lines:
            line = line.strip()
            if not line:
                continue

            log_entry = self._parse_log_line(line)
            if not log_entry:
                continue

            # 消息过滤 - 只显示接收到的消息
            if message_only:
                message = log_entry['message']
                # 检查是否是消息相关的日志
                message_keywords = [
                    '收到私聊消息', '收到群聊', '私聊 - 用户:', '群聊 ',
                    '消息内容:', '内容:', 'process_message'
                ]
                if not any(keyword in message for keyword in message_keywords):
                    continue

            # 应用其他过滤器
            if level_filter and log_entry['level'] != level_filter:
                continue

            if search_query:
                search_text = f"{log_entry['message']} {log_entry['time']}".lower()
                if search_query.lower() not in search_text:
                    continue

            logs.append(log_entry)

        return logs

    def _parse_log_line(self, line):
        """解析单行日志"""
        import re

        # 支持多种日志格式
        # 格式1: 2025-06-04 12:31:07 | INFO     | __main__:check_environment:43 - 检查运行环境...
        # 格式2: 2025-06-04 21:23:25 [INFO ] 正在启动LarkAgentX主应用...
        # 格式3: 06-04 23:19:16 [INFO ] 收到私聊消息 - 用户: 李锦春, 内容: 12321

        # 尝试格式1 (完整日期 + 管道分隔)
        pattern1 = r'^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) \| (\w+)\s+\| .* - (.+)$'
        match1 = re.match(pattern1, line)
        if match1:
            timestamp, level, message = match1.groups()
            return {
                'time': timestamp,
                'level': level.strip(),
                'message': message.strip(),
                'timestamp': timestamp  # 用于排序
            }

        # 尝试格式2 (完整日期 + 方括号)
        pattern2 = r'^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) \[(\w+)\s*\] (.+)$'
        match2 = re.match(pattern2, line)
        if match2:
            timestamp, level, message = match2.groups()
            return {
                'time': timestamp,
                'level': level.strip(),
                'message': message.strip(),
                'timestamp': timestamp  # 用于排序
            }

        # 尝试格式3 (短日期 + 方括号)
        pattern3 = r'^(\d{2}-\d{2} \d{2}:\d{2}:\d{2}) \[(\w+)\s*\] (.+)$'
        match3 = re.match(pattern3, line)
        if match3:
            timestamp, level, message = match3.groups()
            # 为短日期格式添加年份
            full_timestamp = f"2025-{timestamp}"
            return {
                'time': full_timestamp,
                'level': level.strip(),
                'message': message.strip(),
                'timestamp': full_timestamp  # 用于排序
            }

        # 如果都不匹配，返回None
        return None

    def _validate_config(self):
        """验证配置状态"""
        validation = {
            'music_file_exists': False,
            'music_file_path': '',
            'lark_cookie_configured': False,
            'phone_config_complete': False,
            'errors': [],
            'warnings': []
        }

        try:
            # 检查音乐文件
            from app.config.settings import settings
            music_file = settings.get_current_music_file()
            validation['music_file_path'] = music_file

            if os.path.exists(music_file):
                validation['music_file_exists'] = True
            else:
                validation['errors'].append(f"音乐文件不存在: {music_file}")
                # 检查备用文件
                backup_files = [
                    "static/audio/notification.mp3",
                    "static/audio/notification.wav"
                ]
                for backup in backup_files:
                    if os.path.exists(backup):
                        validation['warnings'].append(f"可使用备用文件: {backup}")
                        break

            # 检查飞书Cookie（使用原始值验证）
            lark_cookie = config_manager.get_raw_config('LARK_COOKIE')
            if lark_cookie and len(lark_cookie.strip()) > 10:
                validation['lark_cookie_configured'] = True
            else:
                validation['errors'].append("飞书Cookie未配置或格式不正确")

            # 检查电话提醒配置（敏感信息使用原始值验证）
            app_id = config_manager.get_raw_config('LARK_APP_ID')
            app_secret = config_manager.get_raw_config('LARK_APP_SECRET')
            user_ids = config_manager.get_config('PHONE_NOTIFICATION_USER_IDS')
            message_ids = config_manager.get_config('PHONE_NOTIFICATION_MESSAGE_IDS')

            if app_id and app_secret and user_ids and message_ids:
                validation['phone_config_complete'] = True
            else:
                validation['warnings'].append("电话提醒配置不完整，该功能将无法使用")

        except Exception as e:
            validation['errors'].append(f"配置验证失败: {str(e)}")

        return validation
    
    def start(self):
        """启动Web服务器"""
        if self.is_running:
            logger.warning("Web服务器已在运行中")
            return

        def run_server():
            try:
                logger.info(f"启动Web管理界面: http://{self.host}:{self.port}")

                # 禁用Flask/Werkzeug的访问日志
                log = logging.getLogger('werkzeug')
                log.setLevel(logging.ERROR)

                self.app.run(host=self.host, port=self.port, debug=False, use_reloader=False)
            except Exception as e:
                logger.error(f"Web服务器启动失败: {str(e)}")

        self.server_thread = threading.Thread(target=run_server, daemon=True)
        self.server_thread.start()
        self.is_running = True

        # 等待一下确保服务器启动
        time.sleep(1)
        logger.info("Web管理界面启动成功")
    
    def stop(self):
        """停止Web服务器"""
        if not self.is_running:
            return
        
        self.is_running = False
        logger.info("Web服务器已停止")


# 全局Web服务器实例
web_server = WebServer()
