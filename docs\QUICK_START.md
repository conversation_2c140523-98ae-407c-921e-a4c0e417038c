# LarkFlow 快速开始指南

## 🚀 快速启动

### 1. 项目初始化

```bash
# 运行项目初始化脚本
python scripts/setup_project.py
```

这个脚本会：
- 创建 `.env` 配置文件
- 创建必要的目录结构
- 检查依赖包安装情况

### 2. 配置环境

编辑 `.env` 文件，配置必要参数：

```env
# 飞书 Cookie（必须）
LARK_COOKIE="your_lark_cookie_here"

# 消息匹配模式
TRIGGER_PATTERN="已接通人工.*?@.*?为你服务.*?请问.*?帮你"

# 自动回复内容
REPLY_MESSAGE="您好！有什么可以帮您？"

# 音乐提醒功能
ENABLE_MUSIC_NOTIFICATION=true
NOTIFICATION_MUSIC_FILE="static/audio/notification.mp3"

# 电话提醒功能
ENABLE_PHONE_NOTIFICATION=true
LARK_APP_ID="your_app_id_here"
LARK_APP_SECRET="your_app_secret_here"
PHONE_NOTIFICATION_USER_IDS="user_id_1,user_id_2"
PHONE_NOTIFICATION_MESSAGE_IDS="msg_id_1,msg_id_2,msg_id_3,msg_id_4"
```

### 3. 准备音乐文件

在 `static/audio/` 目录下放置音乐文件：
- 支持格式：MP3、WAV、OGG
- 建议文件名：`notification.mp3`
- 建议时长：10-30秒

### 4. 验证配置

```bash
# 运行一键修复脚本
python quick_fix.py
```

### 5. 启动程序

```bash
python main.py
```

## 🔧 常用工具

### 清理缓存

```bash
python scripts/clean_cache.py
```

### 手动发送电话提醒

```bash
python scripts/manual_phone_reminder.py
```

## 📋 故障排除

### 1. 依赖包问题

```bash
pip install -r requirements.txt
```

### 2. 音乐播放问题

- 检查音乐文件是否存在
- 确认浏览器音频权限已启用
- 确认音频设备正常

### 3. 电话提醒问题

- 检查飞书应用配置
- 确认应用权限
- 验证用户ID和消息ID

### 4. 配置问题

- 检查 .env 文件格式
- 确认必要配置项已填写
- 运行配置测试

## 📚 更多文档

- [项目结构说明](../PROJECT_STRUCTURE.md)
- [音乐功能文档](MUSIC_NOTIFICATION.md)
- [电话功能文档](PHONE_NOTIFICATION.md)
- [开发指南](DEVELOPMENT.md)
